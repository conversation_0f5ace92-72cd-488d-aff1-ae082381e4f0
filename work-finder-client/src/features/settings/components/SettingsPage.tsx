import { useTranslation } from "react-i18next";
import { Settings } from "lucide-react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { ProfileSettings } from "./ProfileSettings";
import { PasswordSettings } from "./PasswordSettings";
import { NotificationSettings } from "./NotificationSettings";
import { PrivacySettings } from "./PrivacySettings";
import { AccountDeletion } from "./AccountDeletion";

export function SettingsPage() {
  const { t } = useTranslation();

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center space-x-3">
        <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
          <Settings className="h-5 w-5 text-gray-600" />
        </div>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            {t("settings.title", "Account Settings")}
          </h1>
          <p className="text-gray-600">
            {t("settings.subtitle", "Manage your account preferences and privacy settings")}
          </p>
        </div>
      </div>

      {/* Settings Tabs */}
      <Tabs defaultValue="profile" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2 lg:grid-cols-5">
          <TabsTrigger value="profile">
            {t("settings.tabs.profile", "Profile")}
          </TabsTrigger>
          <TabsTrigger value="password">
            {t("settings.tabs.password", "Password")}
          </TabsTrigger>
          <TabsTrigger value="notifications">
            {t("settings.tabs.notifications", "Notifications")}
          </TabsTrigger>
          <TabsTrigger value="privacy">
            {t("settings.tabs.privacy", "Privacy")}
          </TabsTrigger>
          <TabsTrigger value="account" className="text-red-600">
            {t("settings.tabs.account", "Account")}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="profile">
          <ProfileSettings />
        </TabsContent>

        <TabsContent value="password">
          <PasswordSettings />
        </TabsContent>

        <TabsContent value="notifications">
          <NotificationSettings />
        </TabsContent>

        <TabsContent value="privacy">
          <PrivacySettings />
        </TabsContent>

        <TabsContent value="account">
          <AccountDeletion />
        </TabsContent>
      </Tabs>
    </div>
  );
}
